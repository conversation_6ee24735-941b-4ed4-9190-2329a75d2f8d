console.log('pay')
class Pay {
  static isPaying = false;
  constructor() {
    console.log('pay初始化')
  }
  async pay(product_id) {
    if (this.isPaying) {
      return
    }
    this.isPaying = true
    console.log('pay', product_id)
    const res = await fetch('https://logs.uggamer.com/order/createOrderId?productId=' + product_id + '&userId=' + window.__roleID, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then((res) => {
      return res.json()
    }).catch((err) => {
      console.log(err)
    })
    console.log('pay', res)
    if (res.code === 200) {
      logReport('ugd_purchase_should', {
        product_id,
        price: res.data[0].price
      })
      return new Promise((resolve, reject) => {
        if (window.flutter_inappwebview) {
          // window.flutterObj.googlePay('com.ol.fishstorm.survival.io.shelltype6', res.data[0].orderId).then(async (googleRes) => {
          window.flutterObj.googlePay(res.data[0].googleProductId, res.data[0].orderId).then(async (googleRes) => {
            console.log(googleRes, 'googleRes')

            const res1 = await fetch('https://logs.uggamer.com/product/checkProductPayResult?orderId=' + res.data[0].orderId, {
              method: 'get',
              headers: {
                'Content-Type': 'application/json'
              }
            }).then((resLog) => {
              return resLog.json()
            }).catch((err) => {
              console.log(err)
            })
            console.log(res1, 'res1')
            if(res1.data[0].orderStatus === 2) {
              logReport('ugd_purchase_suc')
              logReport('ugd_purchase', {
                product_id,
                price: res.data[0].price,
                purchase_state: true,
                extra_reward: res1.data[0].extraReward,
                first_pay: res1.data[0].firstPay
              })
            }
          }).catch((err) => {
            logReport('ugd_purchase_fail')
            logReport('ugd_purchase', {
              product_id,
              price: res.data[0].price,
              purchase_state: false,
              extra_reward: '',
              first_pay: ''
            })
            reject(err)
          }).finally(() => {
            this.isPaying = false
          })
        } else {
          resolve(true)
        }
      })
    }
  }
}

window.payObj = new Pay()